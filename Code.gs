/**
 * Domain Availability Checker - Google Apps Script Backend
 * Sử dụng Namecheap API để kiểm tra tính khả dụng và giá của domain names
 * 
 * <AUTHOR> Agent
 * @version 1.0
 */

// Cấu hình API Namecheap
const NAMECHEAP_CONFIG = {
  API_KEY: '0e89853c585e4fccb6957193e7f71a09',
  USERNAME: 'apiexample', // <PERSON><PERSON>n đư<PERSON> cập nhật với username thực tế
  SANDBOX_URL: 'https://api.sandbox.namecheap.com/xml.response',
  PRODUCTION_URL: 'https://api.namecheap.com/xml.response',
  USE_SANDBOX: true, // Đặt false khi sử dụng production
  RATE_LIMIT_DELAY: 3000, // 3 giây giữa các calls (20 calls/minute)
  MAX_DOMAINS_PER_REQUEST: 50,
  CACHE_DURATION: 5 * 60 * 1000 // 5 phút
};

/**
 * Hàm chính để serve HTML page
 */
function doGet() {
  return HtmlService.createTemplateFromFile('Index')
    .evaluate()
    .setTitle('Domain Availability Checker')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * Include function để load CSS và JS files
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Hàm chính để kiểm tra domain availability và pricing
 * @param {Array} domains - Danh sách domains cần kiểm tra
 * @return {Object} Kết quả kiểm tra
 */
function checkDomains(domains) {
  try {
    // Validate input
    if (!domains || !Array.isArray(domains) || domains.length === 0) {
      throw new Error('Danh sách domain không hợp lệ');
    }

    // Validate domain format
    const validDomains = domains.filter(domain => isValidDomain(domain.trim()));
    if (validDomains.length === 0) {
      throw new Error('Không có domain nào hợp lệ trong danh sách');
    }

    // Check cache first
    const cachedResults = getCachedResults(validDomains);
    const uncachedDomains = validDomains.filter(domain => !cachedResults[domain]);

    let results = { ...cachedResults };

    // Process uncached domains in batches
    if (uncachedDomains.length > 0) {
      const batches = chunkArray(uncachedDomains, NAMECHEAP_CONFIG.MAX_DOMAINS_PER_REQUEST);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        // Add delay between batches for rate limiting
        if (i > 0) {
          Utilities.sleep(NAMECHEAP_CONFIG.RATE_LIMIT_DELAY);
        }

        const batchResults = processDomainBatch(batch);
        results = { ...results, ...batchResults };
        
        // Cache results
        cacheResults(batchResults);
      }
    }

    return {
      success: true,
      data: results,
      totalChecked: validDomains.length,
      availableCount: Object.values(results).filter(r => r.available).length,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error in checkDomains:', error);
    return {
      success: false,
      error: error.message || 'Đã xảy ra lỗi không xác định',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Xử lý một batch domains
 * @param {Array} domains - Batch domains cần xử lý
 * @return {Object} Kết quả batch
 */
function processDomainBatch(domains) {
  const results = {};
  
  try {
    // Gọi API check availability
    const availabilityData = checkDomainAvailability(domains);
    
    // Gọi API get pricing cho các domains available
    const availableDomains = Object.keys(availabilityData).filter(domain => availabilityData[domain].available);
    const pricingData = availableDomains.length > 0 ? getDomainPricing(availableDomains) : {};
    
    // Combine results
    for (const domain of domains) {
      const availability = availabilityData[domain] || { available: false, error: 'Không thể kiểm tra' };
      const pricing = pricingData[domain] || { price: 'N/A', currency: 'USD' };
      
      results[domain] = {
        domain: domain,
        available: availability.available,
        price: pricing.price,
        currency: pricing.currency,
        registrationPeriod: pricing.period || '1 year',
        isPremium: availability.isPremium || false,
        error: availability.error || null
      };
    }
    
  } catch (error) {
    console.error('Error in processDomainBatch:', error);
    // Return error results for all domains in batch
    for (const domain of domains) {
      results[domain] = {
        domain: domain,
        available: false,
        price: 'N/A',
        currency: 'USD',
        registrationPeriod: 'N/A',
        isPremium: false,
        error: 'Lỗi API: ' + error.message
      };
    }
  }
  
  return results;
}

/**
 * Kiểm tra domain availability sử dụng Namecheap API
 * @param {Array} domains - Danh sách domains
 * @return {Object} Kết quả availability
 */
function checkDomainAvailability(domains) {
  const url = NAMECHEAP_CONFIG.USE_SANDBOX ? NAMECHEAP_CONFIG.SANDBOX_URL : NAMECHEAP_CONFIG.PRODUCTION_URL;
  const domainList = domains.join(',');
  
  const params = {
    'ApiUser': NAMECHEAP_CONFIG.USERNAME,
    'ApiKey': NAMECHEAP_CONFIG.API_KEY,
    'UserName': NAMECHEAP_CONFIG.USERNAME,
    'Command': 'namecheap.domains.check',
    'ClientIp': getClientIp(),
    'DomainList': domainList
  };
  
  const response = makeApiRequest(url, params);
  return parseAvailabilityResponse(response);
}

/**
 * Lấy thông tin pricing từ Namecheap API
 * @param {Array} domains - Danh sách domains
 * @return {Object} Thông tin pricing
 */
function getDomainPricing(domains) {
  const url = NAMECHEAP_CONFIG.USE_SANDBOX ? NAMECHEAP_CONFIG.SANDBOX_URL : NAMECHEAP_CONFIG.PRODUCTION_URL;
  
  const params = {
    'ApiUser': NAMECHEAP_CONFIG.USERNAME,
    'ApiKey': NAMECHEAP_CONFIG.API_KEY,
    'UserName': NAMECHEAP_CONFIG.USERNAME,
    'Command': 'namecheap.users.getPricing',
    'ClientIp': getClientIp(),
    'ProductType': 'DOMAIN',
    'ProductCategory': 'REGISTER'
  };
  
  const response = makeApiRequest(url, params);
  return parsePricingResponse(response, domains);
}

/**
 * Thực hiện API request với retry logic
 * @param {string} url - API URL
 * @param {Object} params - Parameters
 * @return {string} Response text
 */
function makeApiRequest(url, params) {
  const maxRetries = 3;
  let lastError;

  // Validate inputs
  if (!url || !params) {
    throw new Error('URL và parameters không được để trống');
  }

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const fullUrl = `${url}?${queryString}`;

      // Check URL length (some APIs have limits)
      if (fullUrl.length > 8192) {
        throw new Error('URL quá dài, vui lòng giảm số lượng domains');
      }

      const response = UrlFetchApp.fetch(fullUrl, {
        method: 'GET',
        muteHttpExceptions: true,
        headers: {
          'User-Agent': 'Domain Checker App/1.0',
          'Accept': 'application/xml, text/xml'
        },
        timeout: 30000 // 30 seconds timeout
      });

      const responseCode = response.getResponseCode();
      const responseText = response.getContentText();

      if (responseCode === 200) {
        // Validate response is XML
        if (!responseText.trim().startsWith('<?xml')) {
          throw new Error('Phản hồi API không phải định dạng XML hợp lệ');
        }
        return responseText;
      } else if (responseCode === 429) {
        // Rate limit exceeded
        throw new Error('Đã vượt quá giới hạn tốc độ API. Vui lòng thử lại sau.');
      } else if (responseCode >= 500) {
        // Server error - retry
        throw new Error(`Lỗi server API (${responseCode}): ${responseText}`);
      } else {
        // Client error - don't retry
        throw new Error(`Lỗi API (${responseCode}): ${responseText}`);
      }

    } catch (error) {
      lastError = error;
      console.error(`API request attempt ${attempt} failed:`, error);

      // Don't retry for certain errors
      if (error.message.includes('URL quá dài') ||
          error.message.includes('không được để trống') ||
          (error.message.includes('Lỗi API') && !error.message.includes('500'))) {
        throw error;
      }

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff with cap
        console.log(`Waiting ${delay}ms before retry...`);
        Utilities.sleep(delay);
      }
    }
  }

  throw new Error(`API request failed after ${maxRetries} attempts: ${lastError.message}`);
}

/**
 * Parse XML response cho domain availability
 * @param {string} xmlResponse - XML response từ API
 * @return {Object} Parsed availability data
 */
function parseAvailabilityResponse(xmlResponse) {
  const results = {};
  
  try {
    const document = XmlService.parse(xmlResponse);
    const root = document.getRootElement();
    
    // Check for API errors
    const errors = root.getChild('Errors');
    if (errors && errors.getChildren().length > 0) {
      const errorMsg = errors.getChildren()[0].getText();
      throw new Error(`API Error: ${errorMsg}`);
    }
    
    const commandResponse = root.getChild('CommandResponse');
    const domainResults = commandResponse.getChildren('DomainCheckResult');
    
    for (const domainResult of domainResults) {
      const domain = domainResult.getAttribute('Domain').getValue();
      const available = domainResult.getAttribute('Available').getValue() === 'true';
      const isPremium = domainResult.getAttribute('IsPremiumName').getValue() === 'true';
      
      results[domain] = {
        available: available,
        isPremium: isPremium
      };
    }
    
  } catch (error) {
    console.error('Error parsing availability response:', error);
    throw new Error('Không thể phân tích phản hồi từ API');
  }
  
  return results;
}

/**
 * Parse XML response cho pricing information
 * @param {string} xmlResponse - XML response từ API
 * @param {Array} domains - Danh sách domains
 * @return {Object} Parsed pricing data
 */
function parsePricingResponse(xmlResponse, domains) {
  const results = {};
  
  try {
    const document = XmlService.parse(xmlResponse);
    const root = document.getRootElement();
    
    // Check for API errors
    const errors = root.getChild('Errors');
    if (errors && errors.getChildren().length > 0) {
      const errorMsg = errors.getChildren()[0].getText();
      throw new Error(`API Error: ${errorMsg}`);
    }
    
    const commandResponse = root.getChild('CommandResponse');
    const userGetPricingResult = commandResponse.getChild('UserGetPricingResult');
    const productType = userGetPricingResult.getChild('ProductType');
    const productCategory = productType.getChild('ProductCategory');
    const products = productCategory.getChildren('Product');
    
    // Create pricing map by TLD
    const pricingMap = {};
    for (const product of products) {
      const tld = product.getAttribute('Name').getValue();
      const prices = product.getChildren('Price');
      
      if (prices.length > 0) {
        const price = prices[0]; // Get first price (usually 1 year)
        pricingMap[tld] = {
          price: parseFloat(price.getAttribute('Price').getValue()),
          currency: price.getAttribute('Currency').getValue(),
          period: `${price.getAttribute('Duration').getValue()} ${price.getAttribute('DurationType').getValue().toLowerCase()}`
        };
      }
    }
    
    // Map pricing to domains
    for (const domain of domains) {
      const tld = domain.split('.').pop();
      if (pricingMap[tld]) {
        results[domain] = pricingMap[tld];
      } else {
        results[domain] = {
          price: 'N/A',
          currency: 'USD',
          period: '1 year'
        };
      }
    }
    
  } catch (error) {
    console.error('Error parsing pricing response:', error);
    // Return default pricing for all domains
    for (const domain of domains) {
      results[domain] = {
        price: 'N/A',
        currency: 'USD',
        period: '1 year'
      };
    }
  }
  
  return results;
}

/**
 * Utility functions
 */

/**
 * Validate domain name format
 * @param {string} domain - Domain name
 * @return {boolean} True if valid
 */
function isValidDomain(domain) {
  if (!domain || typeof domain !== 'string') return false;

  const trimmed = domain.trim();

  // Check length constraints
  if (trimmed.length < 3 || trimmed.length > 253) return false;

  // Check for invalid characters
  if (trimmed.includes(' ') || trimmed.includes('@') || trimmed.includes('_')) return false;

  // Check for consecutive dots
  if (trimmed.includes('..')) return false;

  // Check if starts or ends with dot or hyphen
  if (trimmed.startsWith('.') || trimmed.endsWith('.') ||
      trimmed.startsWith('-') || trimmed.endsWith('-')) return false;

  // Basic domain validation regex
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;

  // Additional check for valid TLD
  const parts = trimmed.split('.');
  if (parts.length < 2) return false;

  const tld = parts[parts.length - 1];
  if (tld.length < 2 || !/^[a-zA-Z]+$/.test(tld)) return false;

  return domainRegex.test(trimmed);
}

/**
 * Chia array thành các chunks nhỏ hơn
 * @param {Array} array - Array cần chia
 * @param {number} size - Kích thước mỗi chunk
 * @return {Array} Array of chunks
 */
function chunkArray(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * Lấy client IP (mock function cho Google Apps Script)
 * @return {string} IP address
 */
function getClientIp() {
  // Google Apps Script không thể lấy real client IP
  // Sử dụng IP mặc định cho sandbox testing
  return '***********';
}

/**
 * Cache management functions
 */

/**
 * Lấy kết quả đã cache
 * @param {Array} domains - Danh sách domains
 * @return {Object} Cached results
 */
function getCachedResults(domains) {
  const cache = PropertiesService.getScriptProperties();
  const results = {};

  for (const domain of domains) {
    const cacheKey = `domain_${domain}`;
    const cachedData = cache.getProperty(cacheKey);

    if (cachedData) {
      try {
        const parsed = JSON.parse(cachedData);
        const now = new Date().getTime();

        // Check if cache is still valid
        if (now - parsed.timestamp < NAMECHEAP_CONFIG.CACHE_DURATION) {
          results[domain] = parsed.data;
        } else {
          // Remove expired cache
          cache.deleteProperty(cacheKey);
        }
      } catch (error) {
        console.error('Error parsing cached data for', domain, error);
        cache.deleteProperty(cacheKey);
      }
    }
  }

  return results;
}

/**
 * Cache kết quả
 * @param {Object} results - Results to cache
 */
function cacheResults(results) {
  const cache = PropertiesService.getScriptProperties();
  const timestamp = new Date().getTime();

  for (const [domain, data] of Object.entries(results)) {
    const cacheKey = `domain_${domain}`;
    const cacheData = {
      data: data,
      timestamp: timestamp
    };

    try {
      cache.setProperty(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error caching data for', domain, error);
    }
  }
}

/**
 * Xóa cache cũ (cleanup function)
 */
function clearExpiredCache() {
  const cache = PropertiesService.getScriptProperties();
  const allProperties = cache.getProperties();
  const now = new Date().getTime();

  for (const [key, value] of Object.entries(allProperties)) {
    if (key.startsWith('domain_')) {
      try {
        const parsed = JSON.parse(value);
        if (now - parsed.timestamp >= NAMECHEAP_CONFIG.CACHE_DURATION) {
          cache.deleteProperty(key);
        }
      } catch (error) {
        // Remove invalid cache entries
        cache.deleteProperty(key);
      }
    }
  }
}

/**
 * Export functions
 */

/**
 * Tạo CSV content từ results
 * @param {Object} results - Domain check results
 * @return {string} CSV content
 */
function generateCSV(results) {
  const headers = ['Domain', 'Status', 'Price (USD)', 'Registration Period', 'Is Premium'];
  const rows = [headers];

  for (const [domain, data] of Object.entries(results)) {
    const row = [
      domain,
      data.available ? 'Available' : 'Taken',
      data.price,
      data.registrationPeriod,
      data.isPremium ? 'Yes' : 'No'
    ];
    rows.push(row);
  }

  return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
}

/**
 * Export results to CSV
 * @param {Object} results - Domain check results
 * @return {Object} Export result
 */
function exportToCSV(results) {
  try {
    const csvContent = generateCSV(results);

    return {
      success: true,
      data: csvContent,
      filename: `domain_check_${new Date().toISOString().split('T')[0]}.csv`
    };
  } catch (error) {
    console.error('Error exporting CSV:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Test functions
 */

/**
 * Test function để kiểm tra API connection
 * @return {Object} Test result
 */
function testApiConnection() {
  try {
    const testDomains = ['test123456789.com'];
    const result = checkDomains(testDomains);

    return {
      success: true,
      message: 'API connection successful',
      data: result
    };
  } catch (error) {
    return {
      success: false,
      message: 'API connection failed',
      error: error.message
    };
  }
}
