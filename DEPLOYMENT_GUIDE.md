# Hướng dẫn Deploy Domain Availability Checker

## Tổng quan

Webapp này được xây dựng trên Google Apps Script và sử dụng Namecheap API để kiểm tra domain availability và pricing. Hướng dẫn này sẽ giúp bạn deploy webapp từ đầu đến cuối.

## Yêu cầu trước khi bắt đầu

### 1. <PERSON><PERSON><PERSON> kho<PERSON>n cần thiết
- **Google Account**: Đ<PERSON> sử dụng Google Apps Script
- **Namecheap Account**: Đ<PERSON> lấy API credentials

### 2. Chuẩn bị API Credentials

#### Bước 1: Đăng ký Namecheap API
1. Đăng nhập vào [Namecheap Dashboard](https://ap.www.namecheap.com/)
2. Vào **Profile** > **Tools** > **Business & Dev Tools** > **API Access**
3. Enable API access
4. Lấy thông tin:
   - **API Key**: Sẽ được hiển thị sau khi enable
   - **Username**: Username Namecheap của bạn
   - **Client IP**: IP address của server (cho production)

#### Bước 2: Whitelist IP (Production)
- Để sử dụng production API, cần whitelist IP
- Sandbox không cần whitelist IP
- **Quan trọng**: Add IP `**************` vào whitelist để tránh spam detection
- Có thể whitelist multiple IPs nếu cần

**Cách whitelist IP:**
1. Vào Namecheap Dashboard > Profile > Tools > API Access
2. Trong mục "Whitelisted IPs", click "Add IP"
3. Nhập IP: `**************`
4. Save changes

## Hướng dẫn Deploy từng bước

### Bước 1: Tạo Google Apps Script Project

1. **Truy cập Google Apps Script**
   - Vào [script.google.com](https://script.google.com)
   - Đăng nhập với Google Account

2. **Tạo Project mới**
   - Click "New Project"
   - Đặt tên: "Domain Availability Checker"
   - Click "Save"

### Bước 2: Setup Files

#### File 1: Code.gs (Backend Logic)
1. Trong Apps Script Editor, file `Code.gs` đã có sẵn
2. Xóa nội dung mặc định
3. Copy toàn bộ nội dung từ file `Code.gs` trong project này
4. **Quan trọng**: Cập nhật thông tin API trong `NAMECHEAP_CONFIG`:

```javascript
const NAMECHEAP_CONFIG = {
  API_KEY: 'YOUR_ACTUAL_API_KEY', // Thay bằng API key thực tế
  USERNAME: 'YOUR_NAMECHEAP_USERNAME', // Thay bằng username thực tế
  SANDBOX_URL: 'https://api.sandbox.namecheap.com/xml.response',
  PRODUCTION_URL: 'https://api.namecheap.com/xml.response',
  USE_SANDBOX: true, // Đặt false khi ready cho production
  RATE_LIMIT_DELAY: 3000,
  MAX_DOMAINS_PER_REQUEST: 50,
  CACHE_DURATION: 5 * 60 * 1000
};
```

#### File 2: Index.html (Frontend)
1. Click "+" để tạo file mới
2. Chọn "HTML file"
3. Đặt tên: "Index"
4. Copy nội dung từ file `Index.html`

#### File 3: Style.html (CSS)
1. Tạo HTML file mới
2. Đặt tên: "Style"
3. Copy nội dung từ file `Style.html`

#### File 4: Script.html (JavaScript)
1. Tạo HTML file mới
2. Đặt tên: "Script"
3. Copy nội dung từ file `Script.html`

#### File 5: Test.gs (Optional)
1. Tạo Script file mới
2. Đặt tên: "Test"
3. Copy nội dung từ file `Test.gs`

### Bước 3: Test Functions

1. **Test API Connection**
   ```javascript
   // Trong Apps Script Editor, chạy function này
   function testSetup() {
     const result = testApiConnection();
     console.log(result);
   }
   ```

2. **Run Test Suite**
   ```javascript
   // Chạy toàn bộ test suite
   runAllTests();
   ```

3. **Kiểm tra Logs**
   - Vào **View** > **Logs** để xem kết quả
   - Hoặc **View** > **Executions** để xem chi tiết

### Bước 4: Deploy Web App

1. **Chuẩn bị Deploy**
   - Đảm bảo tất cả files đã được save
   - Test functions chạy thành công

2. **Deploy Process**
   - Click **Deploy** > **New deployment**
   - Chọn type: **Web app**
   - Cấu hình deployment:

   ```
   Description: Domain Availability Checker v1.0
   Execute as: Me (<EMAIL>)
   Who has access: Anyone
   ```

3. **Authorize**
   - Click **Deploy**
   - Authorize permissions khi được yêu cầu
   - Review và accept permissions

4. **Get URL**
   - Copy **Web app URL** được tạo
   - URL có dạng: `https://script.google.com/macros/s/SCRIPT_ID/exec`

### Bước 5: Test Deployment

1. **Mở webapp**
   - Paste URL vào browser
   - Webapp sẽ load với giao diện đầy đủ

2. **Test API Connection**
   - Click nút "Test API" trong webapp
   - Kiểm tra thông báo thành công

3. **Test với Sample Domains**
   - Click "Tải Mẫu" để load sample domains
   - Click "Kiểm tra Domains"
   - Verify kết quả hiển thị đúng

## Configuration Options

### Sandbox vs Production

#### Sandbox Mode (Recommended for testing)
```javascript
USE_SANDBOX: true
```
- Không cần whitelist IP
- Free testing
- Có thể có data không real-time

#### Production Mode
```javascript
USE_SANDBOX: false
```
- Cần whitelist IP
- Real-time data
- Có thể có charges

### Rate Limiting

```javascript
RATE_LIMIT_DELAY: 3000 // 3 seconds between batches
```
- Namecheap limit: 20 calls/minute
- Adjust delay nếu gặp rate limit errors

### Cache Duration

```javascript
CACHE_DURATION: 5 * 60 * 1000 // 5 minutes
```
- Giảm để có data fresh hơn
- Tăng để giảm API calls

## Troubleshooting

### Lỗi Deploy

1. **"Authorization required"**
   - Re-authorize permissions
   - Check Google Account permissions

2. **"Script function not found"**
   - Verify function names trong Code.gs
   - Check file names chính xác

### Lỗi Runtime

1. **"API connection failed"**
   ```javascript
   // Check configuration
   console.log(NAMECHEAP_CONFIG);
   
   // Test with simple domain
   const result = checkDomains(['test.com']);
   console.log(result);
   ```

2. **"Rate limit exceeded"**
   - Increase `RATE_LIMIT_DELAY`
   - Reduce domains per batch

3. **"Invalid domain format"**
   - Check domain validation function
   - Test với domains đơn giản

### Debug Tips

1. **Enable Logging**
   ```javascript
   console.log('Debug info:', variable);
   ```

2. **Check Execution Transcript**
   - Vào **View** > **Executions**
   - Click vào execution để xem details

3. **Test Individual Functions**
   ```javascript
   // Test validation
   console.log(isValidDomain('test.com'));
   
   // Test API request building
   const params = { test: 'value' };
   console.log(Object.keys(params).map(k => `${k}=${params[k]}`).join('&'));
   ```

## Security Best Practices

### 1. API Key Security
- Không share API key
- Sử dụng sandbox cho development
- Monitor API usage

### 2. Access Control
```javascript
// Trong deployment settings
Who has access: Anyone with the link // Hoặc restrict hơn
```

### 3. Input Validation
- Webapp đã có validation built-in
- Không modify validation logic

## Performance Optimization

### 1. Caching Strategy
- Results cached 5 minutes
- Clear cache khi cần fresh data

### 2. Batch Processing
- Max 50 domains per API call
- Automatic batching implemented

### 3. Error Handling
- Retry logic với exponential backoff
- Graceful degradation

## Monitoring và Maintenance

### 1. Monitor Usage
- Check **View** > **Executions** regularly
- Monitor API quota usage

### 2. Update Dependencies
- Tailwind CSS: Auto-updated từ CDN
- Font Awesome: Auto-updated từ CDN

### 3. Backup
- Export project code định kỳ
- Keep backup của configurations

## Next Steps

Sau khi deploy thành công:

1. **Share URL** với users
2. **Monitor performance** và errors
3. **Collect feedback** để improvements
4. **Consider upgrades**:
   - Custom domain
   - Enhanced features
   - Integration với other services

## Support

Nếu gặp issues:

1. Check logs trong Google Apps Script
2. Verify API credentials
3. Test với simple cases trước
4. Review troubleshooting section

---

**Happy Deploying!** 🚀
