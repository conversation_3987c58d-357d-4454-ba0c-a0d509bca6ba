<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Availability Checker</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom styles -->
    <?!= include('Style'); ?>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i class="fas fa-globe text-blue-600 text-3xl mr-3"></i>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Domain Availability Checker</h1>
                        <p class="text-sm text-gray-600">Kiểm tra tính khả dụng và giá của domain names</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="testApiBtn" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-flask mr-2"></i>Test API
                    </button>
                    <button id="clearCacheBtn" class="bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-trash mr-2"></i>Xóa Cache
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Input Section -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-8">
            <div class="mb-6">
                <label for="domainInput" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-list mr-2"></i>Danh sách Domain Names
                </label>
                <textarea 
                    id="domainInput" 
                    rows="8" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    placeholder="Nhập danh sách domain names, mỗi domain trên một dòng:&#10;&#10;example.com&#10;test.net&#10;mydomain.org&#10;&#10;Tối đa 50 domains mỗi lần kiểm tra"
                ></textarea>
                <div class="mt-2 flex justify-between items-center text-sm text-gray-500">
                    <span>Mỗi domain trên một dòng riêng biệt</span>
                    <span id="domainCount" class="font-medium">0 domains</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4">
                <button 
                    id="checkDomainsBtn" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                >
                    <i class="fas fa-search mr-2"></i>
                    <span>Kiểm tra Domains</span>
                </button>
                
                <button 
                    id="clearInputBtn" 
                    class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                >
                    <i class="fas fa-eraser mr-2"></i>
                    <span>Xóa Danh Sách</span>
                </button>
                
                <button 
                    id="loadSampleBtn" 
                    class="bg-green-100 hover:bg-green-200 text-green-700 px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                >
                    <i class="fas fa-file-import mr-2"></i>
                    <span>Tải Mẫu</span>
                </button>
            </div>
        </div>

        <!-- Progress Section -->
        <div id="progressSection" class="bg-white rounded-lg shadow-sm border p-6 mb-8 hidden">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-cog fa-spin mr-2 text-blue-600"></i>
                    Đang kiểm tra domains...
                </h3>
                <span id="progressText" class="text-sm text-gray-600">0/0</span>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div id="progressBar" class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            
            <div id="progressDetails" class="text-sm text-gray-600">
                Đang chuẩn bị...
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <!-- Summary Stats -->
            <div id="summaryStats" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <!-- Stats cards will be populated by JavaScript -->
            </div>

            <!-- Results Table -->
            <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-table mr-2"></i>Kết Quả Kiểm Tra
                    </h3>
                    <div class="flex space-x-2">
                        <select id="sortSelect" class="border border-gray-300 rounded px-3 py-1 text-sm">
                            <option value="domain">Sắp xếp theo Domain</option>
                            <option value="status">Sắp xếp theo Status</option>
                            <option value="price">Sắp xếp theo Giá</option>
                        </select>
                        <button 
                            id="exportCsvBtn" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors flex items-center"
                        >
                            <i class="fas fa-download mr-2"></i>
                            Export CSV
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Domain Name
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Price (USD)
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Registration Period
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Premium
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Action
                                </th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Results will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="hidden">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl mr-3"></i>
                    <div>
                        <h3 class="text-lg font-medium text-red-800">Đã xảy ra lỗi</h3>
                        <p id="errorMessage" class="text-red-700 mt-1"></p>
                    </div>
                </div>
                <button 
                    id="retryBtn" 
                    class="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded font-medium transition-colors"
                >
                    <i class="fas fa-redo mr-2"></i>Thử lại
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex justify-between items-center text-sm text-gray-600">
                <div>
                    <p>Powered by <strong>Namecheap API</strong> | Built with Google Apps Script</p>
                </div>
                <div>
                    <p>Rate limit: 20 calls/minute | Cache: 5 minutes</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div class="flex items-center">
                <i class="fas fa-spinner fa-spin text-blue-600 text-2xl mr-4"></i>
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Đang xử lý...</h3>
                    <p class="text-gray-600">Vui lòng đợi trong giây lát</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <?!= include('Script'); ?>
</body>
</html>
