# Domain Availability Checker

Webapp hoàn chỉnh để kiểm tra tính khả dụng và giá của domain names sử dụng Namecheap API, chạy trên nền tảng Google Apps Script.

## Tính năng chính

### 🔍 Kiểm tra Domain
- Kiểm tra tính khả dụng của domain names
- Lấy thông tin giá đăng ký
- Hỗ trợ batch checking (tối đa 50 domains/lần)
- Phát hiện premium domains

### 🎨 Giao diện hiện đại
- Responsive design với Tailwind CSS
- Loading indicators và progress bars
- Animations và transitions mượt mà
- Dark mode support (tùy chọn)

### ⚡ Performance
- Caching kết quả trong 5 phút
- Rate limiting tuân thủ API limits
- Retry logic với exponential backoff
- Async processing với progress updates

### 📊 Xuất dữ liệu
- Export kết quả ra CSV
- Bảng kết quả có thể sắp xếp
- Thống kê tổng quan

## Cài đặt và Deploy

### Bước 1: Tạo Google Apps Script Project

1. Truy cập [Google Apps Script](https://script.google.com)
2. Tạo project mới
3. Đặt tên project: "Domain Availability Checker"

### Bước 2: Upload Code

1. **Code.gs**: Copy toàn bộ nội dung từ file `Code.gs`
2. **Index.html**: Tạo file HTML mới, copy nội dung từ `Index.html`
3. **Style.html**: Tạo file HTML mới, copy nội dung từ `Style.html`
4. **Script.html**: Tạo file HTML mới, copy nội dung từ `Script.html`
5. **Test.gs** (tùy chọn): Copy nội dung từ `Test.gs` để test

### Bước 3: Cấu hình API

Trong file `Code.gs`, cập nhật thông tin API:

```javascript
const NAMECHEAP_CONFIG = {
  API_KEY: '0e89853c585e4fccb6957193e7f71a09', // API Key của bạn
  USERNAME: 'your-namecheap-username', // Thay bằng username thực tế
  SANDBOX_URL: 'https://api.sandbox.namecheap.com/xml.response',
  PRODUCTION_URL: 'https://api.namecheap.com/xml.response',
  USE_SANDBOX: true, // Đặt false khi sử dụng production
  RATE_LIMIT_DELAY: 3000,
  MAX_DOMAINS_PER_REQUEST: 50,
  CACHE_DURATION: 5 * 60 * 1000
};
```

### Bước 4: Deploy Web App

1. Trong Google Apps Script, click **Deploy** > **New deployment**
2. Chọn type: **Web app**
3. Cấu hình:
   - Execute as: **Me**
   - Who has access: **Anyone** (hoặc theo nhu cầu)
4. Click **Deploy**
5. Copy URL được tạo

### Bước 5: Test

1. Mở URL webapp
2. Click nút "Test API" để kiểm tra kết nối
3. Thử với sample domains

## Sử dụng

### Kiểm tra Domains

1. **Nhập domains**: Paste danh sách domains vào textarea, mỗi domain một dòng
2. **Validation**: Hệ thống sẽ tự động validate format domains
3. **Kiểm tra**: Click "Kiểm tra Domains" để bắt đầu
4. **Xem kết quả**: Kết quả hiển thị trong bảng với thông tin:
   - Domain name
   - Status (Available/Taken)
   - Price (USD)
   - Registration period
   - Premium status

### Export CSV

1. Sau khi có kết quả, click "Export CSV"
2. File CSV sẽ được download với format:
   ```
   Domain,Status,Price (USD),Registration Period,Is Premium
   example.com,Available,10.99,1 year,No
   taken.com,Taken,N/A,N/A,No
   ```

### Tính năng khác

- **Load Sample**: Tải domains mẫu để test
- **Clear Cache**: Xóa cache để force refresh
- **Sort Results**: Sắp xếp kết quả theo domain, status, hoặc price

## API Configuration

### Namecheap API Setup

1. **Đăng ký tài khoản** Namecheap
2. **Enable API access** trong account settings
3. **Lấy API key** từ dashboard
4. **Whitelist IP** (cho production) hoặc sử dụng sandbox

### Rate Limits

- **Sandbox**: 20 calls/minute
- **Production**: 20 calls/minute
- **Max domains per call**: 50

### Endpoints sử dụng

- `namecheap.domains.check`: Kiểm tra availability
- `namecheap.users.getPricing`: Lấy thông tin giá

## Troubleshooting

### Lỗi thường gặp

1. **"API connection failed"**
   - Kiểm tra API key và username
   - Đảm bảo IP được whitelist (production)
   - Thử chuyển sang sandbox mode

2. **"Rate limit exceeded"**
   - Đợi 1 phút trước khi thử lại
   - Giảm số lượng domains trong một lần check

3. **"Invalid domain format"**
   - Kiểm tra format domains
   - Loại bỏ spaces và ký tự đặc biệt

4. **"URL quá dài"**
   - Giảm số lượng domains (< 30 domains/lần)
   - Chia thành nhiều batch nhỏ hơn

### Debug Mode

Để enable debug mode, mở Developer Console (F12) và xem logs.

## Testing

Chạy test suite:

```javascript
// Trong Google Apps Script Editor
runAllTests();
```

Test cases bao gồm:
- Domain validation
- API request building
- Cache operations
- CSV generation
- Error handling
- Performance testing

## Security Notes

- API key được lưu trong server-side code (an toàn)
- Không expose sensitive data ra client
- Rate limiting để tránh abuse
- Input validation để tránh injection

## Performance Tips

1. **Sử dụng cache**: Kết quả được cache 5 phút
2. **Batch processing**: Gộp nhiều domains trong một request
3. **Async processing**: UI không bị block khi processing
4. **Debounced input**: Giảm số lần validate khi typing

## Customization

### Thay đổi styling

Chỉnh sửa file `Style.html` để customize:
- Colors và themes
- Animations
- Responsive breakpoints
- Dark mode

### Thêm tính năng

Có thể extend với:
- Whois lookup
- Domain monitoring
- Price alerts
- Bulk registration

## Support

Nếu gặp vấn đề:

1. Kiểm tra logs trong Google Apps Script
2. Verify API credentials
3. Test với domains đơn giản trước
4. Đảm bảo có internet connection ổn định

## License

MIT License - Free to use and modify.

---

**Developed by**: Augment Agent  
**Version**: 1.0  
**Last Updated**: 2025-01-25
