<script>
/**
 * Domain Availability Checker - Client-side JavaScript
 * Handles UI interactions and API calls to Google Apps Script backend
 */

// Global variables
let currentResults = {};
let isProcessing = false;

// DOM elements
const domainInput = document.getElementById('domainInput');
const domainCount = document.getElementById('domainCount');
const checkDomainsBtn = document.getElementById('checkDomainsBtn');
const clearInputBtn = document.getElementById('clearInputBtn');
const loadSampleBtn = document.getElementById('loadSampleBtn');
const testApiBtn = document.getElementById('testApiBtn');
const clearCacheBtn = document.getElementById('clearCacheBtn');
const progressSection = document.getElementById('progressSection');
const progressBar = document.getElementById('progressBar');
const progressText = document.getElementById('progressText');
const progressDetails = document.getElementById('progressDetails');
const resultsSection = document.getElementById('resultsSection');
const summaryStats = document.getElementById('summaryStats');
const resultsTableBody = document.getElementById('resultsTableBody');
const sortSelect = document.getElementById('sortSelect');
const exportCsvBtn = document.getElementById('exportCsvBtn');
const errorSection = document.getElementById('errorSection');
const errorMessage = document.getElementById('errorMessage');
const retryBtn = document.getElementById('retryBtn');
const loadingOverlay = document.getElementById('loadingOverlay');

// Sample domains for testing
const sampleDomains = [
    'example-test-123.com',
    'my-awesome-site.net',
    'test-domain-456.org',
    'sample-website.info',
    'demo-site-789.co'
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateDomainCount();
    
    // Add welcome animation
    document.body.classList.add('fade-in');
});

/**
 * Initialize all event listeners
 */
function initializeEventListeners() {
    // Domain input events
    domainInput.addEventListener('input', updateDomainCount);
    domainInput.addEventListener('paste', function() {
        setTimeout(updateDomainCount, 100);
    });

    // Button events
    checkDomainsBtn.addEventListener('click', handleCheckDomains);
    clearInputBtn.addEventListener('click', handleClearInput);
    loadSampleBtn.addEventListener('click', handleLoadSample);
    testApiBtn.addEventListener('click', handleTestApi);
    clearCacheBtn.addEventListener('click', handleClearCache);
    exportCsvBtn.addEventListener('click', handleExportCsv);
    retryBtn.addEventListener('click', handleCheckDomains);

    // Sort functionality
    sortSelect.addEventListener('change', handleSortResults);

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'Enter':
                    e.preventDefault();
                    if (!isProcessing) handleCheckDomains();
                    break;
                case 'k':
                    e.preventDefault();
                    domainInput.focus();
                    break;
            }
        }
    });
}

/**
 * Update domain count display
 */
function updateDomainCount() {
    const domains = getDomainList();
    const count = domains.length;
    domainCount.textContent = `${count} domain${count !== 1 ? 's' : ''}`;
    
    // Update button state
    checkDomainsBtn.disabled = count === 0 || isProcessing;
    
    // Add visual feedback
    if (count > 50) {
        domainCount.classList.add('text-red-600');
        domainCount.textContent += ' (Tối đa 50 domains)';
    } else {
        domainCount.classList.remove('text-red-600');
    }
}

/**
 * Get cleaned domain list from input
 */
function getDomainList() {
    const text = domainInput.value.trim();
    if (!text) return [];
    
    return text.split('\n')
        .map(domain => domain.trim())
        .filter(domain => domain.length > 0)
        .slice(0, 50); // Limit to 50 domains
}

/**
 * Handle check domains button click
 */
async function handleCheckDomains() {
    const domains = getDomainList();
    
    if (domains.length === 0) {
        showError('Vui lòng nhập ít nhất một domain name');
        return;
    }

    // Validate domains
    const invalidDomains = domains.filter(domain => !isValidDomainFormat(domain));
    if (invalidDomains.length > 0) {
        showError(`Các domain không hợp lệ: ${invalidDomains.join(', ')}`);
        return;
    }

    try {
        isProcessing = true;
        hideError();
        hideResults();
        showProgress();
        updateProgress(0, domains.length, 'Đang chuẩn bị kiểm tra...');

        // Call backend function
        const result = await callBackendFunction('checkDomains', domains);
        
        if (result.success) {
            currentResults = result.data;
            updateProgress(100, domains.length, 'Hoàn thành!');
            
            setTimeout(() => {
                hideProgress();
                showResults(result);
            }, 1000);
        } else {
            throw new Error(result.error || 'Đã xảy ra lỗi không xác định');
        }
        
    } catch (error) {
        console.error('Error checking domains:', error);
        hideProgress();
        showError(error.message || 'Đã xảy ra lỗi khi kiểm tra domains');
    } finally {
        isProcessing = false;
        updateDomainCount();
    }
}

/**
 * Handle clear input button click
 */
function handleClearInput() {
    domainInput.value = '';
    updateDomainCount();
    hideResults();
    hideError();
    domainInput.focus();
    
    // Add animation
    domainInput.classList.add('success-bounce');
    setTimeout(() => domainInput.classList.remove('success-bounce'), 600);
}

/**
 * Handle load sample button click
 */
function handleLoadSample() {
    domainInput.value = sampleDomains.join('\n');
    updateDomainCount();
    
    // Add animation
    domainInput.classList.add('slide-in');
    setTimeout(() => domainInput.classList.remove('slide-in'), 300);
}

/**
 * Handle test API button click
 */
async function handleTestApi() {
    try {
        showLoadingOverlay();
        const result = await callBackendFunction('testApiConnection');
        hideLoadingOverlay();
        
        if (result.success) {
            showNotification('API connection successful!', 'success');
        } else {
            showNotification('API connection failed: ' + result.error, 'error');
        }
    } catch (error) {
        hideLoadingOverlay();
        showNotification('Test failed: ' + error.message, 'error');
    }
}

/**
 * Handle clear cache button click
 */
async function handleClearCache() {
    try {
        showLoadingOverlay();
        await callBackendFunction('clearExpiredCache');
        hideLoadingOverlay();
        showNotification('Cache cleared successfully!', 'success');
    } catch (error) {
        hideLoadingOverlay();
        showNotification('Failed to clear cache: ' + error.message, 'error');
    }
}

/**
 * Handle export CSV button click
 */
async function handleExportCsv() {
    if (Object.keys(currentResults).length === 0) {
        showError('Không có dữ liệu để export');
        return;
    }

    try {
        showLoadingOverlay();
        const result = await callBackendFunction('exportToCSV', currentResults);
        hideLoadingOverlay();
        
        if (result.success) {
            downloadCSV(result.data, result.filename);
            showNotification('Export CSV thành công!', 'success');
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        hideLoadingOverlay();
        showError('Lỗi export CSV: ' + error.message);
    }
}

/**
 * Handle sort results
 */
function handleSortResults() {
    if (Object.keys(currentResults).length === 0) return;
    
    const sortBy = sortSelect.value;
    const sortedResults = sortResults(currentResults, sortBy);
    renderResultsTable(sortedResults);
}

/**
 * Call backend Google Apps Script function
 */
function callBackendFunction(functionName, ...args) {
    return new Promise((resolve, reject) => {
        google.script.run
            .withSuccessHandler(resolve)
            .withFailureHandler(reject)
            [functionName](...args);
    });
}

/**
 * Validate domain format
 */
function isValidDomainFormat(domain) {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
}

/**
 * Show progress section
 */
function showProgress() {
    progressSection.classList.remove('hidden');
    progressSection.classList.add('fade-in');
}

/**
 * Hide progress section
 */
function hideProgress() {
    progressSection.classList.add('hidden');
    progressSection.classList.remove('fade-in');
}

/**
 * Update progress bar and text
 */
function updateProgress(current, total, details) {
    const percentage = total > 0 ? (current / total) * 100 : 0;
    progressBar.style.width = percentage + '%';
    progressBar.classList.add('progress-bar');
    progressText.textContent = `${current}/${total}`;
    progressDetails.textContent = details;
}

/**
 * Show results section
 */
function showResults(result) {
    hideError();
    
    // Render summary stats
    renderSummaryStats(result);
    
    // Render results table
    renderResultsTable(result.data);
    
    // Show results section
    resultsSection.classList.remove('hidden');
    resultsSection.classList.add('fade-in');
}

/**
 * Hide results section
 */
function hideResults() {
    resultsSection.classList.add('hidden');
    resultsSection.classList.remove('fade-in');
}

/**
 * Render summary statistics
 */
function renderSummaryStats(result) {
    const total = result.totalChecked;
    const available = result.availableCount;
    const taken = total - available;
    const premium = Object.values(result.data).filter(r => r.isPremium).length;
    
    const stats = [
        { label: 'Tổng số', value: total, icon: 'fas fa-list', class: 'stats-card-total' },
        { label: 'Khả dụng', value: available, icon: 'fas fa-check-circle', class: 'stats-card-available' },
        { label: 'Đã đăng ký', value: taken, icon: 'fas fa-times-circle', class: 'stats-card-taken' },
        { label: 'Premium', value: premium, icon: 'fas fa-crown', class: 'stats-card-premium' }
    ];
    
    summaryStats.innerHTML = stats.map(stat => `
        <div class="stats-card">
            <div class="stats-card-icon ${stat.class}">
                <i class="${stat.icon} text-white"></i>
            </div>
            <div class="text-2xl font-bold text-gray-900">${stat.value}</div>
            <div class="text-sm text-gray-600">${stat.label}</div>
        </div>
    `).join('');
}

/**
 * Render results table
 */
function renderResultsTable(results) {
    const sortedResults = sortResults(results, sortSelect.value);

    resultsTableBody.innerHTML = Object.entries(sortedResults).map(([domain, data]) => {
        const statusClass = data.available ? 'status-available' :
                           data.error ? 'status-error' : 'status-taken';
        const statusIcon = data.available ? 'fas fa-check' :
                          data.error ? 'fas fa-exclamation-triangle' : 'fas fa-times';
        const statusText = data.available ? 'Available' :
                          data.error ? 'Error' : 'Taken';

        const priceDisplay = data.price !== 'N/A' ?
            `<span class="price-display">$${data.price}</span>` :
            `<span class="price-na">N/A</span>`;

        const premiumBadge = data.isPremium ?
            `<span class="premium-badge"><i class="fas fa-crown"></i> Premium</span>` : '';

        const actionButton = data.available ?
            `<button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                     onclick="openRegistrationLink('${domain}')">
                <i class="fas fa-external-link-alt mr-1"></i>Register
             </button>` : '';

        return `
            <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <i class="fas fa-globe text-gray-400 mr-2"></i>
                        <span class="font-medium text-gray-900">${domain}</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="${statusClass}">
                        <i class="${statusIcon}"></i>
                        ${statusText}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${priceDisplay}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    ${data.registrationPeriod}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${premiumBadge}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${actionButton}
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * Sort results by specified criteria
 */
function sortResults(results, sortBy) {
    const entries = Object.entries(results);

    entries.sort(([domainA, dataA], [domainB, dataB]) => {
        switch (sortBy) {
            case 'domain':
                return domainA.localeCompare(domainB);
            case 'status':
                if (dataA.available !== dataB.available) {
                    return dataA.available ? -1 : 1; // Available first
                }
                return domainA.localeCompare(domainB);
            case 'price':
                const priceA = parseFloat(dataA.price) || 0;
                const priceB = parseFloat(dataB.price) || 0;
                if (priceA !== priceB) {
                    return priceA - priceB;
                }
                return domainA.localeCompare(domainB);
            default:
                return domainA.localeCompare(domainB);
        }
    });

    return Object.fromEntries(entries);
}

/**
 * Open registration link for domain
 */
function openRegistrationLink(domain) {
    const namecheapUrl = `https://www.namecheap.com/domains/registration/results/?domain=${encodeURIComponent(domain)}`;
    window.open(namecheapUrl, '_blank');
}

/**
 * Download CSV file
 */
function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * Show error message
 */
function showError(message) {
    errorMessage.textContent = message;
    errorSection.classList.remove('hidden');
    errorSection.classList.add('fade-in', 'error-shake');

    setTimeout(() => {
        errorSection.classList.remove('error-shake');
    }, 500);
}

/**
 * Hide error message
 */
function hideError() {
    errorSection.classList.add('hidden');
    errorSection.classList.remove('fade-in');
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    loadingOverlay.classList.remove('hidden');
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    loadingOverlay.classList.add('hidden');
}

/**
 * Show notification toast
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm fade-in ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    const icon = type === 'success' ? 'fas fa-check-circle' :
                type === 'error' ? 'fas fa-exclamation-circle' :
                'fas fa-info-circle';

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="${icon} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * Format number with commas
 */
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * Debounce function for performance
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add debounced version of updateDomainCount
const debouncedUpdateDomainCount = debounce(updateDomainCount, 300);

// Replace the original event listener
if (domainInput) {
    domainInput.removeEventListener('input', updateDomainCount);
    domainInput.addEventListener('input', debouncedUpdateDomainCount);
}
</script>
