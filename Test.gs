/**
 * Test functions for Domain Availability Checker
 * Kiểm tra các edge cases và error handling
 */

/**
 * Test suite chính
 */
function runAllTests() {
  console.log('=== Starting Domain Checker Tests ===');
  
  const results = {
    passed: 0,
    failed: 0,
    errors: []
  };
  
  // Test cases
  const tests = [
    testValidDomainValidation,
    testInvalidDomainValidation,
    testChunkArrayFunction,
    testCacheOperations,
    testCSVGeneration,
    testApiConnectionMock,
    testBatchProcessingLogic,
    testErrorHandling
  ];
  
  for (const test of tests) {
    try {
      console.log(`Running ${test.name}...`);
      const result = test();
      if (result.success) {
        results.passed++;
        console.log(`✓ ${test.name} passed`);
      } else {
        results.failed++;
        results.errors.push(`${test.name}: ${result.error}`);
        console.log(`✗ ${test.name} failed: ${result.error}`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`${test.name}: ${error.message}`);
      console.log(`✗ ${test.name} threw error: ${error.message}`);
    }
  }
  
  console.log('\n=== Test Results ===');
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Total: ${results.passed + results.failed}`);
  
  if (results.errors.length > 0) {
    console.log('\nErrors:');
    results.errors.forEach(error => console.log(`- ${error}`));
  }
  
  return results;
}

/**
 * Test domain validation function
 */
function testValidDomainValidation() {
  const validDomains = [
    'example.com',
    'test-domain.net',
    'my-site.org',
    'subdomain.example.co.uk',
    '123domain.info',
    'a.co'
  ];
  
  for (const domain of validDomains) {
    if (!isValidDomain(domain)) {
      return { success: false, error: `Valid domain ${domain} was rejected` };
    }
  }
  
  return { success: true };
}

/**
 * Test invalid domain validation
 */
function testInvalidDomainValidation() {
  const invalidDomains = [
    '',
    'invalid',
    '.com',
    'domain.',
    'domain..com',
    '-domain.com',
    'domain-.com',
    'domain.c',
    'domain with spaces.com',
    '<EMAIL>'
  ];
  
  for (const domain of invalidDomains) {
    if (isValidDomain(domain)) {
      return { success: false, error: `Invalid domain ${domain} was accepted` };
    }
  }
  
  return { success: true };
}

/**
 * Test chunk array function
 */
function testChunkArrayFunction() {
  const testArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  
  // Test normal chunking
  const chunks3 = chunkArray(testArray, 3);
  if (chunks3.length !== 4) {
    return { success: false, error: `Expected 4 chunks, got ${chunks3.length}` };
  }
  
  if (chunks3[0].length !== 3 || chunks3[3].length !== 1) {
    return { success: false, error: 'Chunk sizes are incorrect' };
  }
  
  // Test edge cases
  const emptyChunks = chunkArray([], 5);
  if (emptyChunks.length !== 0) {
    return { success: false, error: 'Empty array should return empty chunks' };
  }
  
  const singleChunk = chunkArray([1], 5);
  if (singleChunk.length !== 1 || singleChunk[0].length !== 1) {
    return { success: false, error: 'Single element chunking failed' };
  }
  
  return { success: true };
}

/**
 * Test cache operations
 */
function testCacheOperations() {
  const testResults = {
    'test1.com': {
      domain: 'test1.com',
      available: true,
      price: '10.99',
      currency: 'USD',
      registrationPeriod: '1 year',
      isPremium: false
    },
    'test2.com': {
      domain: 'test2.com',
      available: false,
      price: 'N/A',
      currency: 'USD',
      registrationPeriod: 'N/A',
      isPremium: false
    }
  };
  
  try {
    // Test caching
    cacheResults(testResults);
    
    // Test retrieval
    const cached = getCachedResults(['test1.com', 'test2.com', 'test3.com']);
    
    if (!cached['test1.com'] || !cached['test2.com']) {
      return { success: false, error: 'Cached results not retrieved correctly' };
    }
    
    if (cached['test3.com']) {
      return { success: false, error: 'Non-cached result was returned' };
    }
    
    // Test cache expiration (simulate by clearing)
    const cache = PropertiesService.getScriptProperties();
    cache.deleteProperty('domain_test1.com');
    cache.deleteProperty('domain_test2.com');
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Test CSV generation
 */
function testCSVGeneration() {
  const testResults = {
    'example.com': {
      domain: 'example.com',
      available: true,
      price: '10.99',
      registrationPeriod: '1 year',
      isPremium: false
    },
    'taken.com': {
      domain: 'taken.com',
      available: false,
      price: 'N/A',
      registrationPeriod: 'N/A',
      isPremium: false
    }
  };
  
  try {
    const csv = generateCSV(testResults);
    
    // Check headers
    if (!csv.includes('Domain,Status,Price (USD),Registration Period,Is Premium')) {
      return { success: false, error: 'CSV headers are incorrect' };
    }
    
    // Check data rows
    if (!csv.includes('"example.com","Available","10.99","1 year","No"')) {
      return { success: false, error: 'Available domain CSV row is incorrect' };
    }
    
    if (!csv.includes('"taken.com","Taken","N/A","N/A","No"')) {
      return { success: false, error: 'Taken domain CSV row is incorrect' };
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Test API connection (mock)
 */
function testApiConnectionMock() {
  try {
    // Test parameter building
    const testParams = {
      'ApiUser': 'test',
      'ApiKey': 'test123',
      'Command': 'namecheap.domains.check',
      'DomainList': 'test.com'
    };
    
    const queryString = Object.keys(testParams)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(testParams[key])}`)
      .join('&');
    
    if (!queryString.includes('ApiUser=test') || !queryString.includes('DomainList=test.com')) {
      return { success: false, error: 'Query string building failed' };
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Test batch processing logic
 */
function testBatchProcessingLogic() {
  const domains = [];
  for (let i = 1; i <= 75; i++) {
    domains.push(`test${i}.com`);
  }
  
  const batches = chunkArray(domains, NAMECHEAP_CONFIG.MAX_DOMAINS_PER_REQUEST);
  
  // Should create 2 batches: 50 + 25
  if (batches.length !== 2) {
    return { success: false, error: `Expected 2 batches, got ${batches.length}` };
  }
  
  if (batches[0].length !== 50 || batches[1].length !== 25) {
    return { success: false, error: 'Batch sizes are incorrect' };
  }
  
  return { success: true };
}

/**
 * Test error handling
 */
function testErrorHandling() {
  try {
    // Test empty domain list
    const result1 = checkDomains([]);
    if (result1.success) {
      return { success: false, error: 'Empty domain list should fail' };
    }
    
    // Test invalid domain list
    const result2 = checkDomains(['invalid', '.com', 'domain.']);
    if (result2.success) {
      return { success: false, error: 'Invalid domain list should fail' };
    }
    
    // Test null input
    const result3 = checkDomains(null);
    if (result3.success) {
      return { success: false, error: 'Null input should fail' };
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Performance test
 */
function testPerformance() {
  const startTime = new Date().getTime();
  
  // Test with 10 domains
  const domains = [];
  for (let i = 1; i <= 10; i++) {
    domains.push(`perftest${i}.com`);
  }
  
  try {
    // This would normally call the API, but we'll just test the validation
    const validDomains = domains.filter(domain => isValidDomain(domain));
    const batches = chunkArray(validDomains, NAMECHEAP_CONFIG.MAX_DOMAINS_PER_REQUEST);
    
    const endTime = new Date().getTime();
    const duration = endTime - startTime;
    
    console.log(`Performance test completed in ${duration}ms`);
    
    if (duration > 5000) { // 5 seconds
      return { success: false, error: `Performance test took too long: ${duration}ms` };
    }
    
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Integration test với sample data
 */
function testIntegration() {
  console.log('Running integration test...');
  
  const sampleDomains = [
    'example-test-123456.com',
    'my-test-domain-999.net'
  ];
  
  try {
    // Test domain validation
    const validDomains = sampleDomains.filter(domain => isValidDomain(domain));
    if (validDomains.length !== sampleDomains.length) {
      return { success: false, error: 'Sample domains failed validation' };
    }
    
    // Test batching
    const batches = chunkArray(validDomains, NAMECHEAP_CONFIG.MAX_DOMAINS_PER_REQUEST);
    if (batches.length !== 1) {
      return { success: false, error: 'Batching failed for sample domains' };
    }
    
    console.log('Integration test passed');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
